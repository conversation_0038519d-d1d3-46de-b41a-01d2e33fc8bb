using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Test.WPF.Services;
using Test.WPF.Views;
using Microsoft.Extensions.DependencyInjection;
using System.Windows;

namespace Test.WPF.ViewModels
{
    public partial class MainViewModel : ObservableObject
    {
        private readonly IAuthenticationService _authenticationService;

        [ObservableProperty]
        private string _welcomeMessage = string.Empty;

        public MainViewModel(IAuthenticationService authenticationService)
        {
            _authenticationService = authenticationService;
            UpdateWelcomeMessage();
        }

        private void UpdateWelcomeMessage()
        {
            if (_authenticationService.IsAuthenticated && !string.IsNullOrEmpty(_authenticationService.CurrentUser))
            {
                WelcomeMessage = $"欢迎您，{_authenticationService.CurrentUser}！";
            }
            else
            {
                WelcomeMessage = "欢迎使用本系统！";
            }
        }

        [RelayCommand]
        private async Task LogoutAsync()
        {
            await _authenticationService.LogoutAsync();
            
            // 显示登录窗口
            var loginWindow = ((App)Application.Current).ServiceProvider.GetRequiredService<LoginWindow>();
            loginWindow.Show();
            
            // 关闭当前主窗口
            Application.Current.MainWindow?.Close();
        }
    }
}
