using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Reactive.Linq;
using System.Reactive.Subjects;
using System.ComponentModel.DataAnnotations;
using Test.WPF.Models;
using Test.WPF.Services;
using System.Collections;
using System.ComponentModel;

namespace Test.WPF.ViewModels
{
    public partial class LoginViewModel : ObservableValidator
    {
     /// <summary>
     /// 认证服务
     /// </summary>
        private readonly IAuthenticationService _authenticationService; 
        /// <summary>
        /// 用户名
        /// </summary>
        private readonly BehaviorSubject<string> _usernameSubject = new(string.Empty);
        /// <summary>
        /// 密码
        /// </summary>
        private readonly BehaviorSubject<string> _passwordSubject = new(string.Empty);

        [ObservableProperty]
        [Required(ErrorMessage = "用户名不能为空")]
        [StringLength(50, ErrorMessage = "用户名长度不能超过50个字符")]
        private string _username = string.Empty;

        [ObservableProperty]
        [Required(ErrorMessage = "密码不能为空")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "密码长度必须在6-100个字符之间")]
        private string _password = string.Empty;

        [ObservableProperty]
        private bool _rememberMe;

        [ObservableProperty]
        private bool _isLoading;
        /// <summary>
        /// 错误信息
        /// </summary>
        [ObservableProperty]
        private string _errorMessage = string.Empty;
        /// <summary>
        /// 是否可以登录
        /// </summary>
        [ObservableProperty]
        private bool _canLogin;
        /// <summary>
        /// 登录完成事件
        /// </summary>
        public event EventHandler<bool>? LoginCompleted;

        public LoginViewModel(IAuthenticationService authenticationService)
        {
            // 认证服务
            _authenticationService = authenticationService;
            
            // 使用Rx.NET监听用户名和密码变化，实时验证
            _usernameSubject
                // 去除重复的值
                .DistinctUntilChanged()
                // 验证用户名
                .Subscribe(value =>
                {
                    // 用户名赋值
                    Username = value;
                    // 验证用户名
                    ValidateProperty(value, nameof(Username));
                    // 更新是否可以登录
                    UpdateCanLogin();
                });

            _passwordSubject
                .DistinctUntilChanged()
                .Subscribe(value =>
                {
                    Password = value;
                    ValidateProperty(value, nameof(Password));
                    UpdateCanLogin();
                });

            // 组合用户名和密码的变化来确定是否可以登录
            Observable.CombineLatest(
                _usernameSubject,
                _passwordSubject,
                (username, password) => !string.IsNullOrWhiteSpace(username) && !string.IsNullOrWhiteSpace(password))
                .Subscribe(canLogin => CanLogin = canLogin && !HasErrors);

            UpdateCanLogin();
        }

        partial void OnUsernameChanged(string value)
        {
            _usernameSubject.OnNext(value);
            ErrorMessage = string.Empty;
        }

        partial void OnPasswordChanged(string value)
        {
            _passwordSubject.OnNext(value);
            ErrorMessage = string.Empty;
        }

        [RelayCommand(CanExecute = nameof(CanLogin))]
        private async Task LoginAsync()
        {
            if (IsLoading) return;

            ValidateAllProperties();
            if (HasErrors)
            {
                ErrorMessage = "请检查输入信息";
                return;
            }

            IsLoading = true;
            ErrorMessage = string.Empty;

            try
            {
                var loginModel = new LoginModel
                {
                    Username = Username,
                    Password = Password,
                    RememberMe = RememberMe
                };

                bool success = await _authenticationService.LoginAsync(loginModel);
                
                if (success)
                {
                    LoginCompleted?.Invoke(this, true);
                }
                else
                {
                    ErrorMessage = "用户名或密码错误";
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"登录失败: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void UpdateCanLogin()
        {
            CanLogin = !string.IsNullOrWhiteSpace(Username) && 
                      !string.IsNullOrWhiteSpace(Password) && 
                      !HasErrors && 
                      !IsLoading;
            
            LoginCommand.NotifyCanExecuteChanged();
        }

        protected override void OnPropertyChanged(PropertyChangedEventArgs e)
        {
            base.OnPropertyChanged(e);
            
            if (e.PropertyName == nameof(HasErrors) || e.PropertyName == nameof(IsLoading))
            {
                UpdateCanLogin();
            }
        }
    }
}
