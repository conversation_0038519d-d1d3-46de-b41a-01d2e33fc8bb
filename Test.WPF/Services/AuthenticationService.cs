using Test.WPF.Models;

namespace Test.WPF.Services
{
    public class AuthenticationService : IAuthenticationService
    {
        public bool IsAuthenticated { get; private set; }
        public string? CurrentUser { get; private set; }

        public async Task<bool> LoginAsync(LoginModel loginModel)
        {
            // 模拟异步登录验证
            await Task.Delay(100);

            // 简单的演示验证逻辑
            if (loginModel.Username == "admin" && loginModel.Password == "123456")
            {
                IsAuthenticated = true;
                CurrentUser = loginModel.Username;
                return true;
            }

            if (loginModel.Username == "user" && loginModel.Password == "password")
            {
                IsAuthenticated = true;
                CurrentUser = loginModel.Username;
                return true;
            }

            return false;
        }

        public async Task LogoutAsync()
        {
            await Task.Delay(100);
            IsAuthenticated = false;
            CurrentUser = null;
        }
    }
}
