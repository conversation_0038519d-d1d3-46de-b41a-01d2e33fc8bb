using System.Windows;
using System.Windows.Controls;
using Test.WPF.ViewModels;
using Test.WPF.Services;
using Microsoft.Extensions.DependencyInjection;

namespace Test.WPF.Views
{
    public partial class LoginWindow : Window
    {
        private readonly LoginViewModel _viewModel;

        public LoginWindow()
        {
            InitializeComponent();
            
            // 从依赖注入容器获取ViewModel
            _viewModel = ((App)Application.Current).ServiceProvider.GetRequiredService<LoginViewModel>();
            DataContext = _viewModel;

            // 订阅登录完成事件
            _viewModel.LoginCompleted += OnLoginCompleted;

            // 绑定密码框
            PasswordBox.PasswordChanged += OnPasswordChanged;
            
            // 设置焦点
            Loaded += (s, e) => UsernameTextBox.Focus();
        }

        private void OnPasswordChanged(object sender, RoutedEventArgs e)
        {
            if (sender is PasswordBox passwordBox)
            {
                _viewModel.Password = passwordBox.Password;
            }
        }

        private void OnLoginCompleted(object? sender, bool success)
        {
            if (success)
            {
                // 登录成功，打开主窗口
                var mainWindow = ((App)Application.Current).ServiceProvider.GetRequiredService<MainWindow>();
                mainWindow.Show();
                
                // 关闭登录窗口
                this.Close();
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _viewModel.LoginCompleted -= OnLoginCompleted;
            base.OnClosed(e);
        }
    }
}
