<Window x:Class="Test.WPF.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Test.WPF.Views"
        xmlns:vm="clr-namespace:Test.WPF.ViewModels"
        mc:Ignorable="d"
        Title="用户登录" 
        Height="400" 
        Width="350"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="#F5F5F5">
    
    <Window.Resources>
        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="Height" Value="35"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ScrollViewer x:Name="PART_ContentHost"
                                        Margin="{TemplateBinding Padding}"
                                        VerticalAlignment="Center"
                                        Foreground="{TemplateBinding Foreground}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#007ACC"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernPasswordBox" TargetType="PasswordBox">
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="Height" Value="35"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="PasswordBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ScrollViewer x:Name="PART_ContentHost"
                                        Margin="{TemplateBinding Padding}"
                                        VerticalAlignment="Center"
                                        Foreground="{TemplateBinding Foreground}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#007ACC"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Height" Value="40"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Background" Value="#007ACC"/>
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#005A9E"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#004578"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#CCCCCC"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Margin="30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="30"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="10"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="10"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="10"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="10"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="用户登录" 
                   FontSize="24" 
                   FontWeight="Bold" 
                   HorizontalAlignment="Center"
                   Foreground="#333333"/>

        <!-- 用户名 -->
        <TextBlock Grid.Row="2" 
                   Text="用户名:" 
                   FontSize="14" 
                   Foreground="#666666"/>
        
        <TextBox Grid.Row="4" 
                 Style="{StaticResource ModernTextBox}"
                 Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"
                 x:Name="UsernameTextBox"/>

        <!-- 密码 -->
        <TextBlock Grid.Row="6" 
                   Text="密码:" 
                   FontSize="14" 
                   Foreground="#666666"/>
        
        <PasswordBox Grid.Row="8" 
                     Style="{StaticResource ModernPasswordBox}"
                     x:Name="PasswordBox"/>

        <!-- 记住我 -->
        <CheckBox Grid.Row="10" 
                  Content="记住我" 
                  IsChecked="{Binding RememberMe}"
                  FontSize="12"
                  Foreground="#666666"/>

        <!-- 登录按钮 -->
        <Button Grid.Row="12"
                Style="{StaticResource ModernButton}"
                Command="{Binding LoginCommand}"
                IsEnabled="{Binding CanLogin}">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <ProgressBar Width="16" Height="16"
                           Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                           IsIndeterminate="True"
                           Margin="0,0,8,0"/>
                <TextBlock Text="{Binding IsLoading, Converter={StaticResource LoadingTextConverter}, FallbackValue='登录'}"/>
            </StackPanel>
        </Button>

        <!-- 错误信息 -->
        <TextBlock Grid.Row="13"
                   Text="{Binding ErrorMessage}"
                   Foreground="Red"
                   FontSize="12"
                   HorizontalAlignment="Center"
                   Margin="0,10,0,0"
                   TextWrapping="Wrap"
                   Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"/>
    </Grid>
</Window>
