using System.Windows;
using Test.WPF.ViewModels;
using Microsoft.Extensions.DependencyInjection;

namespace Test.WPF;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    public MainWindow()
    {
        InitializeComponent();

        // 从依赖注入容器获取ViewModel
        var viewModel = ((App)Application.Current).ServiceProvider.GetRequiredService<MainViewModel>();
        DataContext = viewModel;
    }
}