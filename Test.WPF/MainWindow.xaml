<Window x:Class="Test.WPF.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Test.WPF"
        mc:Ignorable="d"
        Title="主界面" Height="450" Width="800"
        WindowStartupLocation="CenterScreen">
    <Grid Background="#F5F5F5">
        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
            <TextBlock Text="登录成功！"
                       FontSize="24"
                       FontWeight="Bold"
                       Foreground="#007ACC"
                       HorizontalAlignment="Center"
                       Margin="0,0,0,20"/>

            <TextBlock Text="{Binding WelcomeMessage}"
                       FontSize="16"
                       Foreground="#333333"
                       HorizontalAlignment="Center"
                       Margin="0,0,0,30"/>

            <Button Content="退出登录"
                    Width="120"
                    Height="35"
                    FontSize="14"
                    Background="#DC3545"
                    Foreground="White"
                    BorderThickness="0"
                    Command="{Binding LogoutCommand}">
                <Button.Template>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#C82333"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#BD2130"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Button.Template>
            </Button>
        </StackPanel>
    </Grid>
</Window>
